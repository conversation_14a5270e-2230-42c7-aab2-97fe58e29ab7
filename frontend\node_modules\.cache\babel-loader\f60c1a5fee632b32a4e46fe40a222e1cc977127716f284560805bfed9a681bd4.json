{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\pages\\\\templates\\\\TemplateForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport HtmlEmailEditor from 'components/HtmlEmailEditor';\nimport Input from 'components/Input';\nimport { useNavigate, useParams } from 'react-router-dom';\n// Import services\nimport { templateRecommendationService } from 'services';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemplateForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    templateId\n  } = useParams();\n  const isEditing = Boolean(templateId);\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [category, setCategory] = useState('Other');\n  const [content, setContent] = useState('');\n  const [mjmlContent, setMjmlContent] = useState('');\n  const [thumbnail, setThumbnail] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [step, setStep] = useState(1);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  useEffect(() => {\n    if (isEditing && templateId) {\n      fetchTemplate(templateId);\n    }\n  }, [templateId]);\n  const fetchTemplate = async templateId => {\n    setLoading(true);\n    try {\n      // Use the correct service method\n      const response = await templateRecommendationService.getTemplateById(templateId);\n      if (response.success && response.template) {\n        // The template data is under the 'template' key in the response\n        const template = response.template;\n        setName(template.name);\n        setDescription(template.description);\n        setCategory(template.category);\n        setContent(template.content); // Make sure 'content' is included in API response\n        setThumbnail(template.thumbnail || ''); // Handle potentially missing thumbnail\n\n        // Set MJML content if available\n        if (template.mjmlContent) {\n          setMjmlContent(template.mjmlContent);\n        }\n      } else {\n        setError(response.message || 'Failed to fetch template');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message || 'Failed to fetch template');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle MjmlEditor save event\n  const handleHtmlSave = html => {\n    console.log('Template HTML saved:', html ? `${html.substring(0, 50)}...` : '(empty)');\n    // Update template content\n    setContent(html);\n    setMjmlContent(html);\n    setHasUnsavedChanges(true);\n  };\n  const handleSubmit = async e => {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    if (step === 1) {\n      // Validate first step\n      if (!name || !description || !category) {\n        setError('Please fill in all required fields');\n        return;\n      }\n\n      // Generate a thumbnail if not provided\n      if (!thumbnail) {\n        setThumbnail(`https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`);\n      }\n      setStep(2);\n      return;\n    }\n\n    // Step 2 submission\n    console.log('[Submit Step 2] Starting...');\n\n    // Validate that we have content (both HTML and MJML? or just HTML?)\n    // Let's assume HTML is primary for validation\n    if (!content) {\n      setError('Please create a template design before saving');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    console.log('[Submit Step 2] Preparing API call...');\n    try {\n      const templateData = {\n        name,\n        description,\n        category,\n        content,\n        // HTML content\n        thumbnail: thumbnail || `https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`,\n        mjmlContent: mjmlContent // Added MJML content\n      };\n      console.log('[Submit Step 2] Template data size:', JSON.stringify(templateData).length, 'bytes');\n      let response;\n      if (isEditing && templateId) {\n        console.log('[Submit Step 2] Updating template ID:', templateId);\n        response = await templateRecommendationService.updateTemplate(templateId, templateData);\n      } else {\n        console.log('[Submit Step 2] Creating new template');\n        response = await templateRecommendationService.createTemplate(templateData);\n      }\n      console.log('[Submit Step 2] API Response:', response);\n      if (response.success) {\n        console.log('[Submit Step 2] Success! Setting timeout for navigation.');\n        setSuccess(isEditing ? 'Template updated successfully' : 'Template created successfully');\n\n        // Use window.location for a full page refresh to avoid navigation issues\n        setTimeout(() => {\n          console.log('[Submit Step 2] Navigating back to templates');\n          window.location.href = '/email-templates'; // Update to match the route in App.tsx\n        }, 1500);\n      } else {\n        console.error('[Submit Step 2] API call not successful:', response.message);\n        setError(response.message || (isEditing ? 'Failed to update template' : 'Failed to create template'));\n      }\n    } catch (err) {\n      console.error('[Submit Step 2] Error during API call:', err);\n\n      // Provide more detailed error message based on status code\n      if (err.response) {\n        if (err.response.status === 404) {\n          setError('API endpoint not found. Please check server configuration.');\n        } else if (err.response.status === 413) {\n          setError('Template content is too large. Try simplifying your design.');\n        } else if (err.response.status === 400) {\n          var _err$response$data2;\n          setError(((_err$response$data2 = err.response.data) === null || _err$response$data2 === void 0 ? void 0 : _err$response$data2.message) || 'Invalid template data. Please check your inputs.');\n        } else if (err.response.status >= 500) {\n          setError('Server error. Please try again later or contact support.');\n        } else {\n          var _err$response$data3;\n          setError(((_err$response$data3 = err.response.data) === null || _err$response$data3 === void 0 ? void 0 : _err$response$data3.message) || err.message || 'An error occurred while saving the template.');\n        }\n      } else if (err.request) {\n        setError('No response from server. Check your internet connection and try again.');\n      } else {\n        setError(err.message || 'An unexpected error occurred');\n      }\n    } finally {\n      console.log('[Submit Step 2] Setting loading to false.');\n      setLoading(false);\n    }\n  };\n  const handleBack = () => {\n    if (step === 2) {\n      setStep(1);\n    } else {\n      navigate('/email-templates');\n    }\n  };\n  if (loading && step === 1) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center p-4 bg-gray-800 text-white border-b border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-xl font-semibold\",\n        children: isEditing ? `Editing: ${name}` : 'Create New Template'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          onClick: handleBack,\n          variant: \"secondary\",\n          size: \"sm\",\n          children: step === 1 ? 'Back to Templates' : 'Back to Details'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          onClick: handleSubmit,\n          variant: \"primary\",\n          size: \"sm\",\n          disabled: loading,\n          children: loading ? 'Saving...' : step === 1 ? 'Next: Design' : isEditing ? 'Update & Close' : 'Save & Close'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-grow overflow-hidden\",\n      children: step === 1 ?\n      /*#__PURE__*/\n      // Step 1 Form (centered or styled differently)\n      _jsxDEV(\"div\", {\n        className: \"flex-grow p-6 bg-gray-900 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-2xl mx-auto bg-gray-800 rounded-lg p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-white font-medium\",\n                  children: \"Template Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-0.5 bg-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-gray-600 text-gray-400 rounded-full flex items-center justify-center text-sm font-semibold\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-400 font-medium\",\n                  children: \"Design Template\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n            type: \"error\",\n            message: error,\n            className: \"mb-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 25\n          }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n            type: \"success\",\n            message: success,\n            className: \"mb-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 27\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-white mb-6 text-center\",\n            children: \"Create Template Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-center mb-8\",\n            children: \"Fill in the basic information for your email template. You'll design the template in the next step.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                id: \"templateName\",\n                name: \"templateName\",\n                label: \"Template Name\",\n                type: \"text\",\n                value: name,\n                onChange: e => setName(e.target.value),\n                required: true,\n                placeholder: \"Enter template name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 20\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-white mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: description,\n                onChange: e => setDescription(e.target.value),\n                required: true,\n                placeholder: \"Enter template description\",\n                className: \"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800 resize-none h-32\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 20\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-white mb-2\",\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: category,\n                onChange: e => setCategory(e.target.value),\n                className: \"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Marketing\",\n                  children: \"Marketing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Transactional\",\n                  children: \"Transactional\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Newsletter\",\n                  children: \"Newsletter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Event\",\n                  children: \"Event\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Onboarding\",\n                  children: \"Onboarding\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Follow-up\",\n                  children: \"Follow-up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Promotional\",\n                  children: \"Promotional\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 22\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 20\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                id: \"thumbnailUrl\",\n                name: \"thumbnailUrl\",\n                label: \"Thumbnail URL (optional)\",\n                type: \"text\",\n                value: thumbnail,\n                onChange: e => setThumbnail(e.target.value),\n                placeholder: \"Enter thumbnail URL or leave blank\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 20\n              }, this), thumbnail && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: thumbnail,\n                  alt: \"Thumbnail preview\",\n                  className: \"h-32 object-cover rounded-lg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 56\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"md\",\n                children: \"Next: Design Template\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 20\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // Step 2 Editor Layout\n      _jsxDEV(\"div\", {\n        className: \"flex flex-grow h-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 text-white text-center font-semibold border-b border-gray-700\",\n            children: \"Template Options\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 space-y-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-300 mb-2\",\n                children: \"Help\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Use the editor to create responsive HTML emails. Customize your template using the available blocks and settings.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow flex flex-col bg-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(HtmlEmailEditor, {\n            initialHtml: content,\n            onSave: handleHtmlSave,\n            height: \"calc(100vh - 200px)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(TemplateForm, \"KaOhURlS5Hegi4SrzU7+9IJntqg=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = TemplateForm;\nexport default TemplateForm;\nvar _c;\n$RefreshReg$(_c, \"TemplateForm\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "HtmlEmailEditor", "Input", "useNavigate", "useParams", "templateRecommendationService", "jsxDEV", "_jsxDEV", "TemplateForm", "_s", "navigate", "templateId", "isEditing", "Boolean", "name", "setName", "description", "setDescription", "category", "setCategory", "content", "<PERSON><PERSON><PERSON><PERSON>", "mjml<PERSON><PERSON><PERSON>", "setMjmlContent", "thumbnail", "setThumbnail", "loading", "setLoading", "error", "setError", "success", "setSuccess", "step", "setStep", "hasUnsavedChanges", "setHasUnsavedChanges", "fetchTemplate", "response", "getTemplateById", "template", "message", "err", "_err$response", "_err$response$data", "data", "handleHtmlSave", "html", "console", "log", "substring", "handleSubmit", "e", "preventDefault", "encodeURIComponent", "templateData", "JSON", "stringify", "length", "updateTemplate", "createTemplate", "setTimeout", "window", "location", "href", "status", "_err$response$data2", "_err$response$data3", "request", "handleBack", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "variant", "size", "disabled", "onSubmit", "id", "label", "value", "onChange", "target", "required", "placeholder", "src", "alt", "initialHtml", "onSave", "height", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/templates/TemplateForm.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport HtmlEmailEditor from 'components/HtmlEmailEditor';\nimport Input from 'components/Input';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\n// Import services\nimport { templateRecommendationService } from 'services';\n\nconst TemplateForm: React.FC = () => {\n  const navigate = useNavigate();\n  const { templateId } = useParams<{ templateId: string }>();\n  const isEditing = Boolean(templateId);\n  const [name, setName] = useState<string>('');\n  const [description, setDescription] = useState<string>('');\n  const [category, setCategory] = useState<string>('Other');\n  const [content, setContent] = useState<string>('');\n  const [mjmlContent, setMjmlContent] = useState<string>('');\n  const [thumbnail, setThumbnail] = useState<string>('');\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [step, setStep] = useState<number>(1);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);\n\n  useEffect(() => {\n    if (isEditing && templateId) {\n      fetchTemplate(templateId);\n    }\n  }, [templateId]);\n\n  const fetchTemplate = async (templateId: string) => {\n    setLoading(true);\n    try {\n      // Use the correct service method\n      const response = await templateRecommendationService.getTemplateById(templateId);\n      if (response.success && response.template) {\n        // The template data is under the 'template' key in the response\n        const template = response.template;\n        setName(template.name);\n        setDescription(template.description);\n        setCategory(template.category);\n        setContent(template.content); // Make sure 'content' is included in API response\n        setThumbnail(template.thumbnail || ''); // Handle potentially missing thumbnail\n\n        // Set MJML content if available\n        if (template.mjmlContent) {\n          setMjmlContent(template.mjmlContent);\n        }\n      } else {\n        setError(response.message || 'Failed to fetch template');\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to fetch template');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle MjmlEditor save event\n  const handleHtmlSave = (html: string) => {\n    console.log('Template HTML saved:', html ? `${html.substring(0, 50)}...` : '(empty)');\n    // Update template content\n    setContent(html);\n    setMjmlContent(html);\n    setHasUnsavedChanges(true);\n  };\n\n  const handleSubmit = async (e?: React.FormEvent) => {\n    e?.preventDefault();\n\n    if (step === 1) {\n      // Validate first step\n      if (!name || !description || !category) {\n        setError('Please fill in all required fields');\n        return;\n      }\n\n      // Generate a thumbnail if not provided\n      if (!thumbnail) {\n        setThumbnail(`https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`);\n      }\n\n      setStep(2);\n      return;\n    }\n\n    // Step 2 submission\n    console.log('[Submit Step 2] Starting...');\n\n    // Validate that we have content (both HTML and MJML? or just HTML?)\n    // Let's assume HTML is primary for validation\n    if (!content) {\n      setError('Please create a template design before saving');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    console.log('[Submit Step 2] Preparing API call...');\n\n    try {\n      const templateData = {\n        name,\n        description,\n        category,\n        content, // HTML content\n        thumbnail: thumbnail || `https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`,\n        mjmlContent: mjmlContent, // Added MJML content\n      };\n\n      console.log('[Submit Step 2] Template data size:', JSON.stringify(templateData).length, 'bytes');\n\n      let response;\n\n      if (isEditing && templateId) {\n        console.log('[Submit Step 2] Updating template ID:', templateId);\n        response = await templateRecommendationService.updateTemplate(templateId, templateData);\n      } else {\n        console.log('[Submit Step 2] Creating new template');\n        response = await templateRecommendationService.createTemplate(templateData);\n      }\n\n      console.log('[Submit Step 2] API Response:', response);\n      if (response.success) {\n        console.log('[Submit Step 2] Success! Setting timeout for navigation.');\n        setSuccess(isEditing ? 'Template updated successfully' : 'Template created successfully');\n\n        // Use window.location for a full page refresh to avoid navigation issues\n        setTimeout(() => {\n          console.log('[Submit Step 2] Navigating back to templates');\n          window.location.href = '/email-templates'; // Update to match the route in App.tsx\n        }, 1500);\n      } else {\n        console.error('[Submit Step 2] API call not successful:', response.message);\n        setError(response.message || (isEditing ? 'Failed to update template' : 'Failed to create template'));\n      }\n    } catch (err: any) {\n      console.error('[Submit Step 2] Error during API call:', err);\n\n      // Provide more detailed error message based on status code\n      if (err.response) {\n        if (err.response.status === 404) {\n          setError('API endpoint not found. Please check server configuration.');\n        } else if (err.response.status === 413) {\n          setError('Template content is too large. Try simplifying your design.');\n        } else if (err.response.status === 400) {\n          setError(err.response.data?.message || 'Invalid template data. Please check your inputs.');\n        } else if (err.response.status >= 500) {\n          setError('Server error. Please try again later or contact support.');\n        } else {\n          setError(err.response.data?.message || err.message || 'An error occurred while saving the template.');\n        }\n      } else if (err.request) {\n        setError('No response from server. Check your internet connection and try again.');\n      } else {\n        setError(err.message || 'An unexpected error occurred');\n      }\n    } finally {\n      console.log('[Submit Step 2] Setting loading to false.');\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    if (step === 2) {\n      setStep(1);\n    } else {\n      navigate('/email-templates');\n    }\n  };\n\n  if (loading && step === 1) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-800\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col h-screen\">\n      {/* Optional Header for Template Name/Actions */}\n      <div className=\"flex justify-between items-center p-4 bg-gray-800 text-white border-b border-gray-700\">\n        <h1 className=\"text-xl font-semibold\">\n          {isEditing ? `Editing: ${name}` : 'Create New Template'}\n        </h1>\n        <div className=\"flex gap-2\">\n           <Button\n              type=\"button\"\n              onClick={handleBack}\n              variant=\"secondary\"\n              size=\"sm\"\n            >\n              {step === 1 ? 'Back to Templates' : 'Back to Details'}\n            </Button>\n            <Button\n              type=\"button\"\n              onClick={handleSubmit}\n              variant=\"primary\"\n              size=\"sm\"\n              disabled={loading}\n            >\n              {loading ? 'Saving...' : (step === 1 ? 'Next: Design' : (isEditing ? 'Update & Close' : 'Save & Close'))}\n            </Button>\n        </div>\n      </div>\n\n      {/* Main Editor Area */}\n      <div className=\"flex flex-grow overflow-hidden\">\n        {step === 1 ? (\n          // Step 1 Form (centered or styled differently)\n          <div className=\"flex-grow p-6 bg-gray-900 overflow-y-auto\">\n            <div className=\"max-w-2xl mx-auto bg-gray-800 rounded-lg p-8\">\n              {/* Step Indicator */}\n              <div className=\"mb-8\">\n                <div className=\"flex items-center justify-center space-x-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold\">1</div>\n                    <span className=\"ml-2 text-white font-medium\">Template Details</span>\n                  </div>\n                  <div className=\"w-12 h-0.5 bg-gray-600\"></div>\n                  <div className=\"flex items-center\">\n                    <div className=\"w-8 h-8 bg-gray-600 text-gray-400 rounded-full flex items-center justify-center text-sm font-semibold\">2</div>\n                    <span className=\"ml-2 text-gray-400 font-medium\">Design Template</span>\n                  </div>\n                </div>\n              </div>\n\n              {error && <Alert type=\"error\" message={error} className=\"mb-6\" />}\n              {success && <Alert type=\"success\" message={success} className=\"mb-6\" />}\n\n              <h2 className=\"text-2xl font-bold text-white mb-6 text-center\">Create Template Details</h2>\n              <p className=\"text-gray-400 text-center mb-8\">Fill in the basic information for your email template. You'll design the template in the next step.</p>\n\n              <form onSubmit={handleSubmit}>\n                 <div className=\"mb-6\">\n                   <Input id=\"templateName\" name=\"templateName\" label=\"Template Name\" type=\"text\" value={name} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setName(e.target.value)} required placeholder=\"Enter template name\" />\n                 </div>\n                 <div className=\"mb-6\">\n                   <label className=\"block text-white mb-2\">Description</label>\n                   <textarea value={description} onChange={(e) => setDescription(e.target.value)} required placeholder=\"Enter template description\" className=\"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800 resize-none h-32\" />\n                 </div>\n                 <div className=\"mb-6\">\n                   <label className=\"block text-white mb-2\">Category</label>\n                   <select value={category} onChange={(e) => setCategory(e.target.value)} className=\"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800\">\n                     {/* Categories must match the backend schema enum values */}\n                     <option value=\"Marketing\">Marketing</option>\n                     <option value=\"Transactional\">Transactional</option>\n                     <option value=\"Newsletter\">Newsletter</option>\n                     <option value=\"Event\">Event</option>\n                     <option value=\"Onboarding\">Onboarding</option>\n                     <option value=\"Follow-up\">Follow-up</option>\n                     <option value=\"Promotional\">Promotional</option>\n                     <option value=\"Other\">Other</option>\n                   </select>\n                 </div>\n                 <div className=\"mb-6\">\n                   <Input id=\"thumbnailUrl\" name=\"thumbnailUrl\" label=\"Thumbnail URL (optional)\" type=\"text\" value={thumbnail} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setThumbnail(e.target.value)} placeholder=\"Enter thumbnail URL or leave blank\" />\n                   {thumbnail && <div className=\"mt-2\"><img src={thumbnail} alt=\"Thumbnail preview\" className=\"h-32 object-cover rounded-lg\" /></div>}\n                 </div>\n                <div className=\"flex justify-end\">\n                   <Button type=\"submit\" variant=\"primary\" size=\"md\">Next: Design Template</Button>\n                 </div>\n              </form>\n            </div>\n          </div>\n        ) : (\n          // Step 2 Editor Layout\n          <div className=\"flex flex-grow h-full\">\n            {/* Simplified left panel without AI suggestions */}\n            <div className=\"w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto flex flex-col\">\n              <div className=\"p-2 text-white text-center font-semibold border-b border-gray-700\">Template Options</div>\n              <div className=\"p-4 space-y-4\">\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-300 mb-2\">Help</h3>\n                  <p className=\"text-xs text-gray-400\">Use the editor to create responsive HTML emails. Customize your template using the available blocks and settings.</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Center Panel (HtmlEmailEditor) */}\n            <div className=\"flex-grow flex flex-col bg-gray-700\">\n              <HtmlEmailEditor\n                initialHtml={content}\n                onSave={handleHtmlSave}\n                height=\"calc(100vh - 200px)\"\n              />\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TemplateForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,SAAS,EACTC,QAAQ,QACH,OAAO;AAEd,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SACEC,WAAW,EACXC,SAAS,QACJ,kBAAkB;AACzB;AACA,SAASC,6BAA6B,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAAW,CAAC,GAAGP,SAAS,CAAyB,CAAC;EAC1D,MAAMQ,SAAS,GAAGC,OAAO,CAACF,UAAU,CAAC;EACrC,MAAM,CAACG,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAC5C,MAAM,CAACkB,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAS,OAAO,CAAC;EACzD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAS,EAAE,CAAC;EAClD,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACkC,IAAI,EAAEC,OAAO,CAAC,GAAGnC,QAAQ,CAAS,CAAC,CAAC;EAC3C,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAU,KAAK,CAAC;EAE1ED,SAAS,CAAC,MAAM;IACd,IAAIe,SAAS,IAAID,UAAU,EAAE;MAC3ByB,aAAa,CAACzB,UAAU,CAAC;IAC3B;EACF,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMyB,aAAa,GAAG,MAAOzB,UAAkB,IAAK;IAClDgB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMU,QAAQ,GAAG,MAAMhC,6BAA6B,CAACiC,eAAe,CAAC3B,UAAU,CAAC;MAChF,IAAI0B,QAAQ,CAACP,OAAO,IAAIO,QAAQ,CAACE,QAAQ,EAAE;QACzC;QACA,MAAMA,QAAQ,GAAGF,QAAQ,CAACE,QAAQ;QAClCxB,OAAO,CAACwB,QAAQ,CAACzB,IAAI,CAAC;QACtBG,cAAc,CAACsB,QAAQ,CAACvB,WAAW,CAAC;QACpCG,WAAW,CAACoB,QAAQ,CAACrB,QAAQ,CAAC;QAC9BG,UAAU,CAACkB,QAAQ,CAACnB,OAAO,CAAC,CAAC,CAAC;QAC9BK,YAAY,CAACc,QAAQ,CAACf,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;;QAExC;QACA,IAAIe,QAAQ,CAACjB,WAAW,EAAE;UACxBC,cAAc,CAACgB,QAAQ,CAACjB,WAAW,CAAC;QACtC;MACF,CAAC,MAAM;QACLO,QAAQ,CAACQ,QAAQ,CAACG,OAAO,IAAI,0BAA0B,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBd,QAAQ,CAAC,EAAAa,aAAA,GAAAD,GAAG,CAACJ,QAAQ,cAAAK,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcE,IAAI,cAAAD,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,IAAI,0BAA0B,CAAC;IACpF,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkB,cAAc,GAAIC,IAAY,IAAK;IACvCC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,IAAI,GAAG,GAAGA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC;IACrF;IACA5B,UAAU,CAACyB,IAAI,CAAC;IAChBvB,cAAc,CAACuB,IAAI,CAAC;IACpBX,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMe,YAAY,GAAG,MAAOC,CAAmB,IAAK;IAClDA,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEC,cAAc,CAAC,CAAC;IAEnB,IAAIpB,IAAI,KAAK,CAAC,EAAE;MACd;MACA,IAAI,CAAClB,IAAI,IAAI,CAACE,WAAW,IAAI,CAACE,QAAQ,EAAE;QACtCW,QAAQ,CAAC,oCAAoC,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI,CAACL,SAAS,EAAE;QACdC,YAAY,CAAC,0DAA0D4B,kBAAkB,CAACvC,IAAI,CAAC,EAAE,CAAC;MACpG;MAEAmB,OAAO,CAAC,CAAC,CAAC;MACV;IACF;;IAEA;IACAc,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;IAE1C;IACA;IACA,IAAI,CAAC5B,OAAO,EAAE;MACZS,QAAQ,CAAC,+CAA+C,CAAC;MACzD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdkB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IAEpD,IAAI;MACF,MAAMM,YAAY,GAAG;QACnBxC,IAAI;QACJE,WAAW;QACXE,QAAQ;QACRE,OAAO;QAAE;QACTI,SAAS,EAAEA,SAAS,IAAI,0DAA0D6B,kBAAkB,CAACvC,IAAI,CAAC,EAAE;QAC5GQ,WAAW,EAAEA,WAAW,CAAE;MAC5B,CAAC;MAEDyB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEO,IAAI,CAACC,SAAS,CAACF,YAAY,CAAC,CAACG,MAAM,EAAE,OAAO,CAAC;MAEhG,IAAIpB,QAAQ;MAEZ,IAAIzB,SAAS,IAAID,UAAU,EAAE;QAC3BoC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAErC,UAAU,CAAC;QAChE0B,QAAQ,GAAG,MAAMhC,6BAA6B,CAACqD,cAAc,CAAC/C,UAAU,EAAE2C,YAAY,CAAC;MACzF,CAAC,MAAM;QACLP,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDX,QAAQ,GAAG,MAAMhC,6BAA6B,CAACsD,cAAc,CAACL,YAAY,CAAC;MAC7E;MAEAP,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEX,QAAQ,CAAC;MACtD,IAAIA,QAAQ,CAACP,OAAO,EAAE;QACpBiB,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvEjB,UAAU,CAACnB,SAAS,GAAG,+BAA+B,GAAG,+BAA+B,CAAC;;QAEzF;QACAgD,UAAU,CAAC,MAAM;UACfb,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3Da,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,kBAAkB,CAAC,CAAC;QAC7C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLhB,OAAO,CAACnB,KAAK,CAAC,0CAA0C,EAAES,QAAQ,CAACG,OAAO,CAAC;QAC3EX,QAAQ,CAACQ,QAAQ,CAACG,OAAO,KAAK5B,SAAS,GAAG,2BAA2B,GAAG,2BAA2B,CAAC,CAAC;MACvG;IACF,CAAC,CAAC,OAAO6B,GAAQ,EAAE;MACjBM,OAAO,CAACnB,KAAK,CAAC,wCAAwC,EAAEa,GAAG,CAAC;;MAE5D;MACA,IAAIA,GAAG,CAACJ,QAAQ,EAAE;QAChB,IAAII,GAAG,CAACJ,QAAQ,CAAC2B,MAAM,KAAK,GAAG,EAAE;UAC/BnC,QAAQ,CAAC,4DAA4D,CAAC;QACxE,CAAC,MAAM,IAAIY,GAAG,CAACJ,QAAQ,CAAC2B,MAAM,KAAK,GAAG,EAAE;UACtCnC,QAAQ,CAAC,6DAA6D,CAAC;QACzE,CAAC,MAAM,IAAIY,GAAG,CAACJ,QAAQ,CAAC2B,MAAM,KAAK,GAAG,EAAE;UAAA,IAAAC,mBAAA;UACtCpC,QAAQ,CAAC,EAAAoC,mBAAA,GAAAxB,GAAG,CAACJ,QAAQ,CAACO,IAAI,cAAAqB,mBAAA,uBAAjBA,mBAAA,CAAmBzB,OAAO,KAAI,kDAAkD,CAAC;QAC5F,CAAC,MAAM,IAAIC,GAAG,CAACJ,QAAQ,CAAC2B,MAAM,IAAI,GAAG,EAAE;UACrCnC,QAAQ,CAAC,0DAA0D,CAAC;QACtE,CAAC,MAAM;UAAA,IAAAqC,mBAAA;UACLrC,QAAQ,CAAC,EAAAqC,mBAAA,GAAAzB,GAAG,CAACJ,QAAQ,CAACO,IAAI,cAAAsB,mBAAA,uBAAjBA,mBAAA,CAAmB1B,OAAO,KAAIC,GAAG,CAACD,OAAO,IAAI,8CAA8C,CAAC;QACvG;MACF,CAAC,MAAM,IAAIC,GAAG,CAAC0B,OAAO,EAAE;QACtBtC,QAAQ,CAAC,wEAAwE,CAAC;MACpF,CAAC,MAAM;QACLA,QAAQ,CAACY,GAAG,CAACD,OAAO,IAAI,8BAA8B,CAAC;MACzD;IACF,CAAC,SAAS;MACRO,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxDrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIpC,IAAI,KAAK,CAAC,EAAE;MACdC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC,MAAM;MACLvB,QAAQ,CAAC,kBAAkB,CAAC;IAC9B;EACF,CAAC;EAED,IAAIgB,OAAO,IAAIM,IAAI,KAAK,CAAC,EAAE;IACzB,oBACEzB,OAAA;MAAK8D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD/D,OAAA;QAAK8D,SAAS,EAAC;MAA2E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC;EAEV;EAEA,oBACEnE,OAAA;IAAK8D,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAErC/D,OAAA;MAAK8D,SAAS,EAAC,uFAAuF;MAAAC,QAAA,gBACpG/D,OAAA;QAAI8D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAClC1D,SAAS,GAAG,YAAYE,IAAI,EAAE,GAAG;MAAqB;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACLnE,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACxB/D,OAAA,CAACP,MAAM;UACJ2E,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAER,UAAW;UACpBS,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,IAAI;UAAAR,QAAA,EAERtC,IAAI,KAAK,CAAC,GAAG,mBAAmB,GAAG;QAAiB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACTnE,OAAA,CAACP,MAAM;UACL2E,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAE1B,YAAa;UACtB2B,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,IAAI;UACTC,QAAQ,EAAErD,OAAQ;UAAA4C,QAAA,EAEjB5C,OAAO,GAAG,WAAW,GAAIM,IAAI,KAAK,CAAC,GAAG,cAAc,GAAIpB,SAAS,GAAG,gBAAgB,GAAG;QAAgB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA;MAAK8D,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAC5CtC,IAAI,KAAK,CAAC;MAAA;MACT;MACAzB,OAAA;QAAK8D,SAAS,EAAC,2CAA2C;QAAAC,QAAA,eACxD/D,OAAA;UAAK8D,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAE3D/D,OAAA;YAAK8D,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB/D,OAAA;cAAK8D,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzD/D,OAAA;gBAAK8D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/D,OAAA;kBAAK8D,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3HnE,OAAA;kBAAM8D,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNnE,OAAA;gBAAK8D,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CnE,OAAA;gBAAK8D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/D,OAAA;kBAAK8D,SAAS,EAAC,uGAAuG;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9HnE,OAAA;kBAAM8D,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL9C,KAAK,iBAAIrB,OAAA,CAACR,KAAK;YAAC4E,IAAI,EAAC,OAAO;YAACnC,OAAO,EAAEZ,KAAM;YAACyC,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAChE5C,OAAO,iBAAIvB,OAAA,CAACR,KAAK;YAAC4E,IAAI,EAAC,SAAS;YAACnC,OAAO,EAAEV,OAAQ;YAACuC,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEvEnE,OAAA;YAAI8D,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3FnE,OAAA;YAAG8D,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAmG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAErJnE,OAAA;YAAMyE,QAAQ,EAAE9B,YAAa;YAAAoB,QAAA,gBAC1B/D,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB/D,OAAA,CAACL,KAAK;gBAAC+E,EAAE,EAAC,cAAc;gBAACnE,IAAI,EAAC,cAAc;gBAACoE,KAAK,EAAC,eAAe;gBAACP,IAAI,EAAC,MAAM;gBAACQ,KAAK,EAAErE,IAAK;gBAACsE,QAAQ,EAAGjC,CAAsC,IAAKpC,OAAO,CAACoC,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;gBAACG,QAAQ;gBAACC,WAAW,EAAC;cAAqB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrN,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/D,OAAA;gBAAO8D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5DnE,OAAA;gBAAU4E,KAAK,EAAEnE,WAAY;gBAACoE,QAAQ,EAAGjC,CAAC,IAAKlC,cAAc,CAACkC,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;gBAACG,QAAQ;gBAACC,WAAW,EAAC,4BAA4B;gBAAClB,SAAS,EAAC;cAAyH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpQ,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/D,OAAA;gBAAO8D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDnE,OAAA;gBAAQ4E,KAAK,EAAEjE,QAAS;gBAACkE,QAAQ,EAAGjC,CAAC,IAAKhC,WAAW,CAACgC,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;gBAACd,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,gBAEvL/D,OAAA;kBAAQ4E,KAAK,EAAC,WAAW;kBAAAb,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CnE,OAAA;kBAAQ4E,KAAK,EAAC,eAAe;kBAAAb,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDnE,OAAA;kBAAQ4E,KAAK,EAAC,YAAY;kBAAAb,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CnE,OAAA;kBAAQ4E,KAAK,EAAC,OAAO;kBAAAb,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnE,OAAA;kBAAQ4E,KAAK,EAAC,YAAY;kBAAAb,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CnE,OAAA;kBAAQ4E,KAAK,EAAC,WAAW;kBAAAb,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CnE,OAAA;kBAAQ4E,KAAK,EAAC,aAAa;kBAAAb,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDnE,OAAA;kBAAQ4E,KAAK,EAAC,OAAO;kBAAAb,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB/D,OAAA,CAACL,KAAK;gBAAC+E,EAAE,EAAC,cAAc;gBAACnE,IAAI,EAAC,cAAc;gBAACoE,KAAK,EAAC,0BAA0B;gBAACP,IAAI,EAAC,MAAM;gBAACQ,KAAK,EAAE3D,SAAU;gBAAC4D,QAAQ,EAAGjC,CAAsC,IAAK1B,YAAY,CAAC0B,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;gBAACI,WAAW,EAAC;cAAoC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAClPlD,SAAS,iBAAIjB,OAAA;gBAAK8D,SAAS,EAAC,MAAM;gBAAAC,QAAA,eAAC/D,OAAA;kBAAKiF,GAAG,EAAEhE,SAAU;kBAACiE,GAAG,EAAC,mBAAmB;kBAACpB,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/H,CAAC,eACPnE,OAAA;cAAK8D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC9B/D,OAAA,CAACP,MAAM;gBAAC2E,IAAI,EAAC,QAAQ;gBAACE,OAAO,EAAC,SAAS;gBAACC,IAAI,EAAC,IAAI;gBAAAR,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;MAAA;MAEN;MACAnE,OAAA;QAAK8D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAEpC/D,OAAA;UAAK8D,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtF/D,OAAA;YAAK8D,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzGnE,OAAA;YAAK8D,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B/D,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAI8D,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEnE,OAAA;gBAAG8D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnE,OAAA;UAAK8D,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClD/D,OAAA,CAACN,eAAe;YACdyF,WAAW,EAAEtE,OAAQ;YACrBuE,MAAM,EAAE9C,cAAe;YACvB+C,MAAM,EAAC;UAAqB;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjE,EAAA,CA7RID,YAAsB;EAAA,QACTL,WAAW,EACLC,SAAS;AAAA;AAAAyF,EAAA,GAF5BrF,YAAsB;AA+R5B,eAAeA,YAAY;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}