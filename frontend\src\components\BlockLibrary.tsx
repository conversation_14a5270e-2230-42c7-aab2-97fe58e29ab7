/**
 * Enhanced BlockLibrary component for Driftly Email Generator
 * Displays available blocks with improved UI, categorization, and drag feedback
 */

import React, {
  useCallback,
  useMemo,
  useState,
} from 'react';

import { useDrag } from 'react-dnd';

// Import Block interface from types to ensure consistency
import { Block } from '../types/editor';

// Define ItemTypes if not imported elsewhere
const ItemTypes = {
  LIBRARY_BLOCK: 'library_block'
};

interface BlockLibraryProps {
  blocks: Block[];
  onAddBlock: (block: Block) => void;
}

// Helper function to get category colors
const getCategoryColor = (category: string): string => {
  const colors: Record<string, string> = {
    'Header': 'bg-purple-100 text-purple-700',
    'Content': 'bg-blue-100 text-blue-700',
    'Layout': 'bg-green-100 text-green-700',
    'Footer': 'bg-orange-100 text-orange-700',
    'CTA': 'bg-red-100 text-red-700',
    'Social': 'bg-pink-100 text-pink-700',
    'Product': 'bg-yellow-100 text-yellow-700',
    'Navigation': 'bg-indigo-100 text-indigo-700',
  };
  return colors[category] || 'bg-gray-100 text-gray-700';
};

// Component that renders each individual library block
const LibraryBlock: React.FC<{
  block: Block,
  onAddBlock: (block: Block) => void,
  isVisible: boolean
}> = React.memo(({ block, onAddBlock, isVisible }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.LIBRARY_BLOCK,
    item: { block, type: ItemTypes.LIBRARY_BLOCK },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  }), [block]);

  // Don't render content if block is not visible
  if (!isVisible) {
    return <div className="h-20 bg-gray-100 animate-pulse rounded" />; // Loading placeholder
  }

  return (
    <div
      ref={drag}
      className={`library-block p-3 mb-2 bg-white border rounded-md cursor-move hover:border-blue-400 hover:shadow-md transition-all transform hover:scale-105 ${
        isDragging ? 'opacity-50 border-blue-500 shadow-lg scale-105' : 'border-gray-200'
      }`}
      onClick={() => onAddBlock(block)}
    >
      <div className="flex items-center justify-between mb-2">
        <h5 className="text-sm font-medium text-gray-800 truncate">{block.name}</h5>
        <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
          getCategoryColor(block.category)
        }`}>
          {block.category}
        </span>
      </div>

      {block.thumbnail ? (
        <div className="mt-2 w-full h-16 flex items-center justify-center overflow-hidden bg-gray-50 rounded border">
          <img
            src={block.thumbnail}
            alt={block.name}
            className="max-h-full max-w-full object-contain"
            loading="lazy"
          />
        </div>
      ) : (
        <div className="mt-2 h-16 flex items-center justify-center bg-gray-50 rounded border">
          <div className="text-center">
            <div className="w-8 h-8 mx-auto mb-1 bg-gray-200 rounded flex items-center justify-center">
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <div className="text-xs text-gray-400">No preview</div>
          </div>
        </div>
      )}

      {block.description && (
        <div className="mt-2 text-xs text-gray-500 line-clamp-2">
          {block.description}
        </div>
      )}

      {/* Drag indicator */}
      {isDragging && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-10 rounded-md flex items-center justify-center">
          <div className="text-blue-600 font-medium text-sm">Dragging...</div>
        </div>
      )}
    </div>
  );
});

// Main component for block library
const BlockLibrary: React.FC<BlockLibraryProps> = ({ blocks, onAddBlock }) => {
  const [activeCategory, setActiveCategory] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [visibleBlocks, setVisibleBlocks] = useState<Set<string>>(new Set());

  // Get unique categories for filter tabs
  const categories = useMemo(() => {
    const cats = new Set<string>();
    blocks.forEach(block => {
      if (block.category) cats.add(block.category);
    });
    return ['All', ...Array.from(cats)];
  }, [blocks]);

  // Filter blocks based on category and search term
  const filteredBlocks = useMemo(() => {
    return blocks.filter(block => {
      // Category filter
      if (activeCategory && activeCategory !== 'All' && block.category !== activeCategory) {
        return false;
      }

      // Search filter
      if (searchTerm.trim() !== '') {
        const searchLower = searchTerm.toLowerCase();
        return (
          block.name.toLowerCase().includes(searchLower) ||
          (block.description || '').toLowerCase().includes(searchLower) ||
          (block.category || '').toLowerCase().includes(searchLower)
        );
      }

      return true;
    });
  }, [blocks, activeCategory, searchTerm]);

  // Implement intersection observer for lazy loading
  const blockObserver = useCallback((node: HTMLDivElement | null) => {
    if (!node) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const blockId = entry.target.getAttribute('data-block-id');
            if (blockId) {
              // Fix Set iteration by using current value to create a new Set
              setVisibleBlocks(prev => {
                const newSet = new Set(prev);
                newSet.add(blockId);
                return newSet;
              });
            }
          }
        });
      },
      { rootMargin: '200px 0px' } // Load blocks that are 200px outside viewport
    );

    observer.observe(node);

    return () => observer.disconnect();
  }, []);

  // Cache check for block visibility
  const isBlockVisible = useCallback((blockId: string) => {
    return visibleBlocks.has(blockId);
  }, [visibleBlocks]);

  return (
    <div className="block-library flex flex-col h-full overflow-hidden">
      {/* Search Box */}
      <div className="px-3 py-2 border-b border-gray-200">
        <input
          type="search"
          placeholder="Search blocks..."
          className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Category Tabs */}
      <div className="flex flex-nowrap overflow-x-auto px-3 py-2 border-b border-gray-200 hide-scrollbar">
        {categories.map((category) => (
          <button
            key={category}
            className={`px-3 py-1 mr-2 text-xs font-medium rounded-full whitespace-nowrap transition-colors ${
              activeCategory === category || (category === 'All' && !activeCategory)
                ? 'bg-blue-100 text-blue-700'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            onClick={() => setActiveCategory(category === 'All' ? '' : category)}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Block List - Virtualized */}
      <div className="flex-1 overflow-y-auto p-3">
        {filteredBlocks.length > 0 ? (
          filteredBlocks.map((block) => (
            <div
              key={block.blockId || block._id || `block-${Math.random()}`}
              ref={blockObserver}
              data-block-id={block.blockId || block._id || `block-${Math.random()}`}
            >
              <LibraryBlock
                block={block}
                onAddBlock={onAddBlock}
                isVisible={isBlockVisible(block.blockId || block._id || '')}
              />
            </div>
          ))
        ) : (
          <div className="text-sm text-gray-500 text-center p-4">
            No blocks found. Try a different search or category.
          </div>
        )}
      </div>
    </div>
  );
};

export default React.memo(BlockLibrary);
export { BlockLibrary };
