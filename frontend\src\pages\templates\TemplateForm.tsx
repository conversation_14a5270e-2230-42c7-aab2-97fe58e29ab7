import React, {
  useEffect,
  useState,
} from 'react';

import Alert from 'components/Alert';
import Button from 'components/Button';
import HtmlEmailEditor from 'components/HtmlEmailEditor';
import Input from 'components/Input';
import {
  useNavigate,
  useParams,
} from 'react-router-dom';
// Import services
import { templateRecommendationService } from 'services';

const TemplateForm: React.FC = () => {
  const navigate = useNavigate();
  const { templateId } = useParams<{ templateId: string }>();
  const isEditing = Boolean(templateId);
  const [name, setName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [category, setCategory] = useState<string>('Other');
  const [content, setContent] = useState<string>('');
  const [mjmlContent, setMjmlContent] = useState<string>('');
  const [thumbnail, setThumbnail] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [step, setStep] = useState<number>(1);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);

  useEffect(() => {
    if (isEditing && templateId) {
      fetchTemplate(templateId);
    }
  }, [templateId]);

  const fetchTemplate = async (templateId: string) => {
    setLoading(true);
    try {
      // Use the correct service method
      const response = await templateRecommendationService.getTemplateById(templateId);
      if (response.success && response.template) {
        // The template data is under the 'template' key in the response
        const template = response.template;
        setName(template.name);
        setDescription(template.description);
        setCategory(template.category);
        setContent(template.content); // Make sure 'content' is included in API response
        setThumbnail(template.thumbnail || ''); // Handle potentially missing thumbnail

        // Set MJML content if available
        if (template.mjmlContent) {
          setMjmlContent(template.mjmlContent);
        }
      } else {
        setError(response.message || 'Failed to fetch template');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to fetch template');
    } finally {
      setLoading(false);
    }
  };

  // Handle MjmlEditor save event
  const handleHtmlSave = (html: string) => {
    console.log('Template HTML saved:', html ? `${html.substring(0, 50)}...` : '(empty)');
    // Update template content
    setContent(html);
    setMjmlContent(html);
    setHasUnsavedChanges(true);
  };

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();

    if (step === 1) {
      // Validate first step
      if (!name || !description || !category) {
        setError('Please fill in all required fields');
        return;
      }

      // Generate a thumbnail if not provided
      if (!thumbnail) {
        setThumbnail(`https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`);
      }

      setStep(2);
      return;
    }

    // Step 2 submission
    console.log('[Submit Step 2] Starting...');

    // Validate that we have content (both HTML and MJML? or just HTML?)
    // Let's assume HTML is primary for validation
    if (!content) {
      setError('Please create a template design before saving');
      return;
    }

    setLoading(true);
    setError(null);
    console.log('[Submit Step 2] Preparing API call...');

    try {
      const templateData = {
        name,
        description,
        category,
        content, // HTML content
        thumbnail: thumbnail || `https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`,
        mjmlContent: mjmlContent, // Added MJML content
      };

      console.log('[Submit Step 2] Template data size:', JSON.stringify(templateData).length, 'bytes');

      let response;

      if (isEditing && templateId) {
        console.log('[Submit Step 2] Updating template ID:', templateId);
        response = await templateRecommendationService.updateTemplate(templateId, templateData);
      } else {
        console.log('[Submit Step 2] Creating new template');
        response = await templateRecommendationService.createTemplate(templateData);
      }

      console.log('[Submit Step 2] API Response:', response);
      if (response.success) {
        console.log('[Submit Step 2] Success! Setting timeout for navigation.');
        setSuccess(isEditing ? 'Template updated successfully' : 'Template created successfully');

        // Use window.location for a full page refresh to avoid navigation issues
        setTimeout(() => {
          console.log('[Submit Step 2] Navigating back to templates');
          window.location.href = '/email-templates'; // Update to match the route in App.tsx
        }, 1500);
      } else {
        console.error('[Submit Step 2] API call not successful:', response.message);
        setError(response.message || (isEditing ? 'Failed to update template' : 'Failed to create template'));
      }
    } catch (err: any) {
      console.error('[Submit Step 2] Error during API call:', err);

      // Provide more detailed error message based on status code
      if (err.response) {
        if (err.response.status === 404) {
          setError('API endpoint not found. Please check server configuration.');
        } else if (err.response.status === 413) {
          setError('Template content is too large. Try simplifying your design.');
        } else if (err.response.status === 400) {
          setError(err.response.data?.message || 'Invalid template data. Please check your inputs.');
        } else if (err.response.status >= 500) {
          setError('Server error. Please try again later or contact support.');
        } else {
          setError(err.response.data?.message || err.message || 'An error occurred while saving the template.');
        }
      } else if (err.request) {
        setError('No response from server. Check your internet connection and try again.');
      } else {
        setError(err.message || 'An unexpected error occurred');
      }
    } finally {
      console.log('[Submit Step 2] Setting loading to false.');
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (step === 2) {
      setStep(1);
    } else {
      navigate('/email-templates');
    }
  };

  if (loading && step === 1) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-800"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      {/* Optional Header for Template Name/Actions */}
      <div className="flex justify-between items-center p-4 bg-gray-800 text-white border-b border-gray-700">
        <h1 className="text-xl font-semibold">
          {isEditing ? `Editing: ${name}` : 'Create New Template'}
        </h1>
        <div className="flex gap-2">
           <Button
              type="button"
              onClick={handleBack}
              variant="secondary"
              size="sm"
            >
              {step === 1 ? 'Back to Templates' : 'Back to Details'}
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              variant="primary"
              size="sm"
              disabled={loading}
            >
              {loading ? 'Saving...' : (step === 1 ? 'Next: Design' : (isEditing ? 'Update & Close' : 'Save & Close'))}
            </Button>
        </div>
      </div>

      {/* Main Editor Area */}
      <div className="flex flex-grow overflow-hidden">
        {step === 1 ? (
          // Step 1 Form (centered or styled differently)
          <div className="flex-grow p-6 bg-gray-900 overflow-y-auto">
            <div className="max-w-2xl mx-auto bg-gray-800 rounded-lg p-8">
              {/* Step Indicator */}
              <div className="mb-8">
                <div className="flex items-center justify-center space-x-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                    <span className="ml-2 text-white font-medium">Template Details</span>
                  </div>
                  <div className="w-12 h-0.5 bg-gray-600"></div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-600 text-gray-400 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                    <span className="ml-2 text-gray-400 font-medium">Design Template</span>
                  </div>
                </div>
              </div>

              {error && <Alert type="error" message={error} className="mb-6" />}
              {success && <Alert type="success" message={success} className="mb-6" />}

              <h2 className="text-2xl font-bold text-white mb-6 text-center">Create Template Details</h2>
              <p className="text-gray-400 text-center mb-8">Fill in the basic information for your email template. You'll design the template in the next step.</p>

              <form onSubmit={handleSubmit}>
                 <div className="mb-6">
                   <Input id="templateName" name="templateName" label="Template Name" type="text" value={name} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setName(e.target.value)} required placeholder="Enter template name" />
                 </div>
                 <div className="mb-6">
                   <label className="block text-white mb-2">Description</label>
                   <textarea value={description} onChange={(e) => setDescription(e.target.value)} required placeholder="Enter template description" className="w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800 resize-none h-32" />
                 </div>
                 <div className="mb-6">
                   <label className="block text-white mb-2">Category</label>
                   <select value={category} onChange={(e) => setCategory(e.target.value)} className="w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800">
                     {/* Categories must match the backend schema enum values */}
                     <option value="Marketing">Marketing</option>
                     <option value="Transactional">Transactional</option>
                     <option value="Newsletter">Newsletter</option>
                     <option value="Event">Event</option>
                     <option value="Onboarding">Onboarding</option>
                     <option value="Follow-up">Follow-up</option>
                     <option value="Promotional">Promotional</option>
                     <option value="Other">Other</option>
                   </select>
                 </div>
                 <div className="mb-6">
                   <Input id="thumbnailUrl" name="thumbnailUrl" label="Thumbnail URL (optional)" type="text" value={thumbnail} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setThumbnail(e.target.value)} placeholder="Enter thumbnail URL or leave blank" />
                   {thumbnail && <div className="mt-2"><img src={thumbnail} alt="Thumbnail preview" className="h-32 object-cover rounded-lg" /></div>}
                 </div>
                <div className="flex justify-end">
                   <Button type="submit" variant="primary" size="md">Next: Design Template</Button>
                 </div>
              </form>
            </div>
          </div>
        ) : (
          // Step 2 Editor Layout
          <div className="flex flex-grow h-full">
            {/* Simplified left panel without AI suggestions */}
            <div className="w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto flex flex-col">
              <div className="p-4 border-b border-gray-700">
                {/* Step Indicator for Step 2 */}
                <div className="mb-4">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">✓</div>
                      <span className="ml-1 text-green-400 text-xs font-medium">Details</span>
                    </div>
                    <div className="w-8 h-0.5 bg-green-600"></div>
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">2</div>
                      <span className="ml-1 text-white text-xs font-medium">Design</span>
                    </div>
                  </div>
                </div>
                <div className="text-white text-center font-semibold">Template Editor</div>
              </div>
              <div className="p-4 space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">Template: {name}</h3>
                  <p className="text-xs text-gray-400 mb-3">{description}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">Instructions</h3>
                  <p className="text-xs text-gray-400">Use the drag-and-drop editor to design your email template. Add text, images, buttons, and other elements from the blocks panel.</p>
                </div>
              </div>
            </div>

            {/* Center Panel (HtmlEmailEditor) */}
            <div className="flex-grow flex flex-col bg-gray-700">
              <HtmlEmailEditor
                initialHtml={content}
                onSave={handleHtmlSave}
                height="calc(100vh - 200px)"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateForm;
