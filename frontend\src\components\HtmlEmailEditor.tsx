import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS

// Optional: Import a preset like grapesjs-preset-newsletter if desired for more features
// import 'grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.min.css';
// import grapesjsNewsletter from 'grapesjs-preset-newsletter';
import React, {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

import grapesjs, { Editor } from 'grapesjs';

// Define the Ref type for exposing editor methods
export interface HtmlEmailEditorRef {
  save: () => Promise<{ html: string }>;
  getEditor: () => Editor | null;
}

interface HtmlEmailEditorProps {
  initialHtml?: string; // Use initialHtml as the primary source
  onSave?: (html: string) => void; // Updated signature
  height?: string | number;
}

// Performance optimization: Cache HTML to avoid expensive re-generations
const htmlCache = new Map<string, string>();

// Basic inline CSS styles for email compatibility
const emailBodyStyle = `
  body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    line-height: 1.5;
    color: #333333;
  }
  h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    font-family: Arial, sans-serif;
    color: #333333;
  }
  img {
    max-width: 100%;
    height: auto;
  }
  .container {
    max-width: 600px;
    margin: 0 auto;
  }
  a {
    color: #007bff;
    text-decoration: none;
  }
  p {
    margin: 10px 0;
  }
`;

// Static blocks configuration - no need for useMemo at module level
const getEmailBlocks = [
  {
    id: 'text',
    label: 'Text',
    attributes: {
      class: 'fa fa-font',
      style: 'font-size: 24px;'
    },
    category: 'Content',
    content: {
      type: 'text',
      content: '<p style="padding: 10px 20px; margin: 0;">Add your text here. Edit the text by clicking on it.</p>',
      style: { padding: '10px' },
      activeOnRender: true
    },
  },
  {
    id: 'image',
    label: 'Image',
    attributes: {
      class: 'fa fa-image',
      style: 'font-size: 24px;'
    },
    category: 'Content',
    select: true, // Open asset manager on drop
    content: {
      type: 'image',
      style: { color: '#000', width: '100%' },
      activeOnRender: true
    },
  },
  {
    id: 'button',
    label: 'Button',
    attributes: {
      class: 'fa fa-link',
      style: 'font-size: 24px;'
    },
    category: 'Content',
    content: `<a href="#" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;" data-gjs-type="link">Button Text</a>`
  },
  {
    id: 'divider',
    label: 'Divider',
    attributes: {
      class: 'fa fa-minus',
      style: 'font-size: 24px;'
    },
    category: 'Content',
    content: `<hr style="border-top: 1px solid #ccc; margin: 20px 0;" />`
  },
  {
    id: 'spacer',
    label: 'Spacer',
    attributes: {
      class: 'fa fa-arrows-v',
      style: 'font-size: 24px;'
    },
    category: 'Layout',
    content: `<div style="height: 50px;"></div>`
  },
  {
    id: 'two-columns',
    label: '2 Columns',
    attributes: {
      class: 'fa fa-columns',
      style: 'font-size: 24px;'
    },
    category: 'Layout',
    content: `
      <div style="display: flex; flex-wrap: wrap; margin: 0 -10px;">
        <div style="width: 50%; padding: 0 10px; box-sizing: border-box;">
          <p style="padding: 10px; margin: 0;">First column content</p>
        </div>
        <div style="width: 50%; padding: 0 10px; box-sizing: border-box;">
          <p style="padding: 10px; margin: 0;">Second column content</p>
        </div>
      </div>
    `
  }
];

// Static panel configurations - no need for useMemo at module level
const getPanels = {
  // Default panels
  styleManager: {
    appendTo: '.styles-container'
  },
  layerManager: {
    appendTo: '.layers-container'
  },
  // Custom panel for better visibility of what's happening
  'editor-status': {
    el: '.status-bar',
    appendTo: '.editor-row',
    content: `<div class="editor-status">
                <div class="gjs-sm-sector">
                  <div class="gjs-sm-title">Editor Status</div>
                  <div class="gjs-sm-properties">
                    <div class="status-item" data-status="components">Loading...</div>
                  </div>
                </div>
              </div>`,
  }
};

// Debounce function for performance optimizations
function debounce<F extends (...args: any[]) => any>(func: F, wait: number): (...args: Parameters<F>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return function(...args: Parameters<F>) {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
      timeoutId = null;
    }, wait);
  };
}

// Throttle function to limit execution frequency
function throttle<F extends (...args: any[]) => any>(func: F, limit: number): (...args: Parameters<F>) => void {
  let inThrottle = false;
  let lastFunc: ReturnType<typeof setTimeout>;
  let lastRan: number;

  return function(...args: Parameters<F>) {
    if (!inThrottle) {
      func(...args);
      lastRan = Date.now();
      inThrottle = true;

      setTimeout(() => {
        inThrottle = false;
      }, limit);
    } else {
      clearTimeout(lastFunc);
      lastFunc = setTimeout(() => {
        if (Date.now() - lastRan >= limit) {
          func(...args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  };
}

// Worker function for HTML generation (would normally be in separate file)
const createHTMLGenerationWorker = () => {
  const workerCode = `
    self.onmessage = function(e) {
      const { html, css } = e.data;

      try {
        // Combine HTML and CSS
        const fullHtml = \`<!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <title>Email Template</title>
            <style>\${css}</style>
          </head>
          <body>\${html}</body>
        </html>\`;

        self.postMessage({ html: fullHtml, error: null });
      } catch (error) {
        self.postMessage({ html: null, error: error.message });
      }
    };
  `;

  const blob = new Blob([workerCode], { type: 'application/javascript' });
  return new Worker(URL.createObjectURL(blob));
};

// Main component implementation with memoization
const HtmlEmailEditorComponent = forwardRef<HtmlEmailEditorRef, HtmlEmailEditorProps>((props, ref) => {
  const { initialHtml = '', onSave, height = '500px' } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const [editor, setEditor] = useState<Editor | null>(null);
  const [isEditorReady, setIsEditorReady] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [lastSavedAt, setLastSavedAt] = useState<Date | null>(null);
  const lastHtmlRef = useRef<string>(initialHtml);
  const [activePanel, setActivePanel] = useState<string | null>(null);
  const [worker, setWorker] = useState<Worker | null>(null);

  // Create HTML generation worker once on component mount
  useEffect(() => {
    if (typeof Worker !== 'undefined') {
      const newWorker = createHTMLGenerationWorker();
      setWorker(newWorker);

      return () => {
        newWorker.terminate();
      };
    }
  }, []);

  // Expose methods to parent through ref
  useImperativeHandle(ref, () => ({
    // Save method - get the HTML from the editor
    save: async () => {
      if (!editor) {
        console.error('[HtmlEmailEditor] Editor not initialized');
        return { html: '' };
      }

      try {
        setIsSaving(true);

        // Get HTML and CSS from the editor
        const html = editor.getHtml();
        const css = editor.getCss({ avoidProtected: true });

        // Generate full HTML
        let finalHtml: string;

        // Use worker if available for performance
        if (worker) {
          finalHtml = await new Promise<string>((resolve, reject) => {
            const messageHandler = (e: MessageEvent) => {
              worker.removeEventListener('message', messageHandler);
              if (e.data.error) {
                reject(new Error(e.data.error));
              } else {
                resolve(e.data.html);
              }
            };

            worker.addEventListener('message', messageHandler);
            worker.postMessage({ html, css });
          });
        } else {
          // Fallback to synchronous processing
          finalHtml = `<!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <title>Email Template</title>
              <style>${css}</style>
            </head>
            <body>${html}</body>
          </html>`;
        }

        // Cache the result for future reference
        if (editor.getDirtyCount?.() > 0 || !htmlCache.has(html)) {
          htmlCache.set(html, finalHtml);
        }

        // If onSave callback is provided, call it with the HTML
        if (onSave) {
          onSave(finalHtml);
        }

        // Update the last saved time
        setLastSavedAt(new Date());
        lastHtmlRef.current = html;

        // Clear the dirty state
        if (editor.UndoManager) {
          editor.UndoManager.clear();
        }

        // Or set dirty count directly if UndoManager isn't available
        if (typeof editor.getDirtyCount === 'function') {
          // Force clear dirty count by manually tracking it
          (editor as any)._dirtyCount = 0;
        }

        console.log("[HtmlEmailEditor] Save complete:", new Date().toISOString());
        return { html: finalHtml };
      } catch (error) {
        console.error('[HtmlEmailEditor] Error saving:', error);
        return { html: '' };
      } finally {
        setIsSaving(false);
      }
    },

    // Get the editor instance
    getEditor: () => editor
  }));

  // Create a debounced save function
  const debouncedSave = useCallback(
    debounce(() => {
      if (editor && onSave) {
        // Only save if content has changed
        const currentHtml = editor.getHtml();
        if (currentHtml !== lastHtmlRef.current) {
          console.log('[HtmlEmailEditor] Auto-saving...');
          // Call the save method through ref - safely handled with optional chaining
          if (typeof ref === 'function') {
            // Can't directly call save() on a function ref
            console.warn('[HtmlEmailEditor] Function refs cannot be used for auto-save');
          } else if (ref?.current) {
            ref.current.save();
          }
        }
      }
    }, 500), // 500ms debounce
    [editor, onSave, ref]
  );

  // Initialize editor when component mounts
  useEffect(() => {
    if (!containerRef.current || editor) return;

    // Create editor with cached options
    const gjsEditor = grapesjs.init({
      // Container where the editor will be mounted
      container: containerRef.current,

      // Disable default storage manager
      storageManager: false,

      // Default HTML when the editor starts
      components: initialHtml,

      // Default style when the editor starts
      style: emailBodyStyle,

      // Load panels on demand to improve initial load time
      panels: { defaults: [] },

      // Disable unused features for better performance
      deviceManager: {
        devices: [
          {
            name: 'Desktop',
            width: '',
          },
          {
            name: 'Tablet',
            width: '768px',
            widthMedia: '992px',
          },
          {
            name: 'Mobile',
            width: '320px',
            widthMedia: '480px',
          },
        ]
      },

      // Configure asset manager with optimization
      assetManager: {
        assets: [],
        uploadFile: undefined, // Disable upload to avoid costly IO
        autoAdd: true,
        embedAsBase64: true, // Inline images as base64 for portability
      },

      // Optimize plugin loading
      plugins: [],

      // Custom configuration for email compatibility
      canvas: {
        styles: [
          'https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700',
          'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css'
        ],
        scripts: []
      },
    });

    // Set editor to state
    setEditor(gjsEditor);

    // Initialize blocks only when needed
    gjsEditor.BlockManager.add('text', {
      label: 'Text',
      attributes: { class: 'fa fa-font' },
      content: {
        type: 'text',
        content: '<p>Add your text here</p>',
        style: { padding: '10px' }
      }
    });

    gjsEditor.BlockManager.add('image', {
      label: 'Image',
      attributes: { class: 'fa fa-image' },
      select: true,
      content: {
        type: 'image',
        style: { padding: '10px', width: '100%' },
        attributes: { src: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"%3E%3Crect width="24" height="24" fill="%23f0f0f0"/%3E%3Cpath d="M12 6.5c-3.04 0-5.5 2.46-5.5 5.5s2.46 5.5 5.5 5.5 5.5-2.46 5.5-5.5-2.46-5.5-5.5-5.5z" fill="%23ddd"/%3E%3C/svg%3E' }
      }
    });

    gjsEditor.BlockManager.add('button', {
      label: 'Button',
      attributes: { class: 'fa fa-square' },
      content: '<a class="button" style="display:inline-block;padding:10px 20px;background-color:#4CAF50;color:#ffffff;text-decoration:none;border-radius:4px;text-align:center;font-weight:bold;">Click me</a>'
    });

    // Add style manager panels on demand for better performance
    gjsEditor.Panels.addPanel({
      id: 'panel-switcher',
      visible: true,
      buttons: [
        {
          id: 'show-style',
          label: 'Styles',
          command: 'show-styles',
          active: false
        },
        {
          id: 'show-layers',
          label: 'Layers',
          command: 'show-layers',
          active: false
        },
        {
          id: 'show-blocks',
          label: 'Blocks',
          command: 'show-blocks',
          active: true
        }
      ]
    });

    // Add commands to switch between panels
    gjsEditor.Commands.add('show-blocks', {
      run: function(editor) {
        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');
        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');
        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');

        if (blocksBtn) blocksBtn.set('active', true);
        if (styleBtn) styleBtn.set('active', false);
        if (layersBtn) layersBtn.set('active', false);

        setActivePanel('blocks');
      }
    });

    gjsEditor.Commands.add('show-styles', {
      run: function(editor) {
        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');
        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');
        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');

        if (blocksBtn) blocksBtn.set('active', false);
        if (styleBtn) styleBtn.set('active', true);
        if (layersBtn) layersBtn.set('active', false);

        setActivePanel('styles');
      }
    });

    gjsEditor.Commands.add('show-layers', {
      run: function(editor) {
        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');
        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');
        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');

        if (blocksBtn) blocksBtn.set('active', false);
        if (styleBtn) styleBtn.set('active', false);
        if (layersBtn) layersBtn.set('active', true);

        setActivePanel('layers');
      }
    });

    // Run the blocks command by default
    gjsEditor.runCommand('show-blocks');

    // Add change event listener
    const throttledOnChange = throttle(() => {
      debouncedSave();
    }, 300);

    gjsEditor.on('change:changesCount', throttledOnChange);

    // Add a beforeunload event listener to warn of unsaved changes
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (gjsEditor.getDirtyCount?.() > 0) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Set editor ready
    setIsEditorReady(true);

    // Clean up editor when component unmounts
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      gjsEditor.off('change:changesCount', throttledOnChange);
      gjsEditor.destroy();
    };
  }, [initialHtml, debouncedSave]);

  // Fetch necessary components based on active panel
  const renderActivePanel = () => {
    if (!activePanel || !editor) return null;

    switch (activePanel) {
      case 'styles':
        return (
          <div className="panel-container styles-container">
            <div className="panel-header">
              <h3>Style Manager</h3>
            </div>
            <div id="style-manager" />
          </div>
        );
      case 'layers':
        return (
          <div className="panel-container layers-container">
            <div className="panel-header">
              <h3>Layers</h3>
            </div>
            <div id="layers-manager" />
          </div>
        );
      case 'blocks':
      default:
        return (
          <div className="panel-container blocks-container">
            <div className="panel-header">
              <h3>Blocks</h3>
            </div>
            <div id="blocks-manager" />
          </div>
        );
    }
  };

  return (
    <div
      className="html-email-editor-container"
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: typeof height === 'number' ? `${height}px` : height,
        border: '1px solid #ddd',
        borderRadius: '4px',
        overflow: 'hidden'
      }}
    >
      {/* Editor Header */}
      <div
        className="editor-header"
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '10px',
          borderBottom: '1px solid #ddd',
          backgroundColor: '#f5f5f5'
        }}
      >
        <div className="editor-title">
          <h2 style={{ margin: 0, fontSize: '16px' }}>HTML Email Editor</h2>
        </div>

        <div className="editor-actions">
          <button
            onClick={() => {
              if (typeof ref === 'function') {
                console.warn('[HtmlEmailEditor] Function refs cannot be used for manual save');
              } else if (ref?.current) {
                ref.current.save();
              }
            }}
            disabled={isSaving || !isEditorReady}
            style={{
              padding: '8px 12px',
              backgroundColor: '#4CAF50',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isSaving || !isEditorReady ? 'not-allowed' : 'pointer',
              opacity: isSaving || !isEditorReady ? 0.7 : 1
            }}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </button>

          {lastSavedAt && (
            <span style={{ marginLeft: '10px', fontSize: '12px', color: '#666' }}>
              Last saved: {lastSavedAt.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {/* Editor Content */}
      <div
        className="editor-content"
        style={{
          display: 'flex',
          flexGrow: 1,
          overflow: 'hidden'
        }}
      >
        {/* Side Panel */}
        <div
          className="editor-sidebar"
          style={{
            width: '250px',
            borderRight: '1px solid #ddd',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: '#f9f9f9'
          }}
        >
          {renderActivePanel()}
        </div>

        {/* Main Editor Canvas */}
        <div
          className="editor-canvas"
          style={{
            flexGrow: 1,
            position: 'relative'
          }}
        >
          {/* Render loading state if editor is not ready */}
          {!isEditorReady && (
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                zIndex: 10
              }}
            >
              <div>Loading editor...</div>
            </div>
          )}

          {/* GrapesJS will be mounted in this div */}
          <div
            ref={containerRef}
            style={{
              height: '100%',
              overflow: 'auto'
            }}
          />
        </div>
      </div>
    </div>
  );
});

// Apply memo to avoid unnecessary re-renders
const HtmlEmailEditor = memo(HtmlEmailEditorComponent);

// Add display name for better debugging
HtmlEmailEditor.displayName = 'HtmlEmailEditor';

export default HtmlEmailEditor;